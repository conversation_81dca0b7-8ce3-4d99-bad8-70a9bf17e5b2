import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  css: {
    postcss: './postcss.config.js',
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    host: '0.0.0.0',
    port: 5173,
    watch: {
      usePolling: true,
    },
    proxy: {
      '/api': {
        target: 'http://nextdoorbuddy-backend:3000',
        changeOrigin: true,
      },
      '/uploads': {
        target: 'http://nextdoorbuddy-backend:3000',
        changeOrigin: true,
        rewrite: (path) => path,
      },
    },
  },
})
