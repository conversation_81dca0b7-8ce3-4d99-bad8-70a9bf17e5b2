<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="SqlDialectMappings">
    <file url="file://$PROJECT_DIR$/backend/src/models/quartier.model.ts" dialect="GenericSQL" />
    <file url="file://$PROJECT_DIR$/docker/init/01_schema.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/docker/init/02_seed.sql" dialect="PostgreSQL" />
    <file url="file://$PROJECT_DIR$/docker/init/04_add_coordinates.sql" dialect="PostgreSQL" />
  </component>
</project>